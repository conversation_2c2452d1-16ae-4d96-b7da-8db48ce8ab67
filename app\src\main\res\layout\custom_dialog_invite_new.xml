<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">

    <RelativeLayout
        style="@style/dialogParentStyle">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/rtl_toolbar"
            android:layout_centerInParent="true"
            android:layout_marginHorizontal="25dp"
            android:layout_marginTop="@dimen/pop_up_top_margin"
            style="@style/popUpBoxbg"
            android:padding="20dp">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="10dp"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <!-- Header Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:layout_marginBottom="25dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="INVITE FRIENDS &amp; EARN"
                            android:textColor="@color/white"
                            android:textSize="22sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Share your referral code and get rewards"
                            android:textColor="@color/colordullwhite"
                            android:textSize="14sp"
                            android:gravity="center" />
                    </LinearLayout>

                    <!-- Referral Code Card -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/transpernt_purple"
                        android:padding="20dp"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:text="Your Referral Code"
                            android:textColor="@color/colordullwhite"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="15dp" />

                        <RelativeLayout
                            android:id="@+id/rltrefer"
                            android:layout_width="match_parent"
                            android:layout_height="55dp"
                            android:background="@drawable/ic_button"
                            android:layout_marginBottom="10dp">

                            <TextView
                                android:id="@+id/txtReferalcode"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                android:text="23423432"
                                android:textColor="#ffffff"
                                android:textSize="20sp"
                                android:textStyle="bold"
                                android:letterSpacing="0.15" />

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:layout_marginEnd="15dp"
                                android:src="@drawable/share"
                                android:background="?selectableItemBackgroundBorderless"
                                android:padding="4dp" />
                        </RelativeLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:text="Tap to copy code"
                            android:textColor="@color/colordullwhite"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <!-- Rewards Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="25dp">

                        <!-- Your Reward -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:background="@drawable/frame"
                            android:padding="15dp"
                            android:layout_marginEnd="10dp"
                            android:gravity="center">

                            <ImageView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:src="@drawable/day5"
                                android:layout_marginBottom="10dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="YOU GET"
                                android:textColor="@color/colordullwhite"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:layout_marginBottom="5dp" />

                            <TextView
                                android:id="@+id/tvInviteCoins"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Rs.60"
                                android:textColor="@color/white"
                                android:textSize="20sp"
                                android:textStyle="bold" />
                        </LinearLayout>

                        <!-- Friend's Reward -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:background="@drawable/frame"
                            android:padding="15dp"
                            android:layout_marginStart="10dp"
                            android:gravity="center">

                            <ImageView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:src="@drawable/day5"
                                android:layout_marginBottom="10dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="FRIEND GETS"
                                android:textColor="@color/colordullwhite"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:layout_marginBottom="5dp" />

                            <TextView
                                android:id="@+id/txtchipsbelow"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Rs.60"
                                android:textColor="@color/white"
                                android:textSize="20sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Share Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/transpernt_purple"
                        android:padding="20dp"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:text="Share via"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="15dp" />

                        <LinearLayout
                            android:id="@+id/lnrsocial"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/imgwhats"
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_marginEnd="20dp"
                                android:src="@drawable/whaatup"
                                android:background="?selectableItemBackgroundBorderless"
                                android:padding="8dp" />

                            <ImageView
                                android:id="@+id/imgfb"
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_marginEnd="20dp"
                                android:src="@drawable/facebook"
                                android:background="?selectableItemBackgroundBorderless"
                                android:padding="8dp" />

                            <ImageView
                                android:id="@+id/imgmail"
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:src="@drawable/mailsocial"
                                android:background="?selectableItemBackgroundBorderless"
                                android:padding="8dp" />
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Terms Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/frame"
                        android:padding="15dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Terms &amp; Conditions"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="10dp" />

                        <TextView
                            android:id="@+id/txtyourfrind"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="INVITE LIMIT 10 FRIENDS"
                            android:textColor="@color/colordullwhite"
                            android:textSize="12sp"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="• Maximum 10 referrals per account\n• Referred friend must be a new user\n• Rewards credited within 24 hours\n• Subject to verification and approval\n• Gaming27.com reserves the right to modify terms"
                            android:textColor="@color/colordullwhite"
                            android:textSize="11sp"
                            android:lineSpacingExtra="2dp" />
                    </LinearLayout>

                </LinearLayout>
            </ScrollView>
        </RelativeLayout>

        <include layout="@layout/dialog_toolbar"/>
    </RelativeLayout>
</RelativeLayout>
