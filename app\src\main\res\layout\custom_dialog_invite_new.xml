<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000">

    <RelativeLayout
        style="@style/dialogParentStyle">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/rtl_toolbar"
            android:layout_centerInParent="true"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="@dimen/pop_up_top_margin"
            style="@style/popUpBoxbg"
            android:padding="25dp">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="10dp"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="5dp">

                    <!-- Header Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:layout_marginBottom="30dp"
                        android:background="@drawable/frame"
                        android:padding="20dp">

                        <ImageView
                            android:layout_width="60dp"
                            android:layout_height="60dp"
                            android:src="@drawable/gift"
                            android:layout_marginBottom="15dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="INVITE &amp; EARN REWARDS"
                            android:textColor="@color/white"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="8dp"
                            android:gravity="center" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Share your code with friends and both get amazing rewards!"
                            android:textColor="@color/colordullwhite"
                            android:textSize="14sp"
                            android:gravity="center"
                            android:lineSpacingExtra="2dp" />
                    </LinearLayout>

                    <!-- Referral Code Card -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/frame"
                        android:padding="25dp"
                        android:layout_marginBottom="25dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:text="Your Referral Code"
                            android:textColor="@color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="20dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:background="#1A1A1A"
                            android:padding="15dp"
                            android:layout_marginBottom="15dp"
                            android:gravity="center_vertical">

                            <TextView
                                android:id="@+id/txtReferalcode"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="GAME2024"
                                android:textColor="#FFD700"
                                android:textSize="22sp"
                                android:textStyle="bold"
                                android:letterSpacing="0.2"
                                android:gravity="center"
                                android:background="@drawable/ic_button"
                                android:padding="12dp" />

                            <LinearLayout
                                android:id="@+id/rltrefer"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:background="@drawable/ic_btn_orange"
                                android:padding="12dp"
                                android:layout_marginStart="10dp"
                                android:gravity="center"
                                android:clickable="true"
                                android:focusable="true">

                                <ImageView
                                    android:layout_width="20dp"
                                    android:layout_height="20dp"
                                    android:src="@drawable/share"
                                    android:layout_marginEnd="8dp"
                                    android:tint="@color/white" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="COPY"
                                    android:textColor="@color/white"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:text="Tap COPY to share with friends"
                            android:textColor="@color/colordullwhite"
                            android:textSize="13sp"
                            android:textStyle="italic" />
                    </LinearLayout>

                    <!-- Rewards Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/frame"
                        android:padding="20dp"
                        android:layout_marginBottom="25dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:text="🎁 REWARDS FOR BOTH 🎁"
                            android:textColor="@color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="20dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center">

                            <!-- Your Reward -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:background="#1A1A1A"
                                android:padding="20dp"
                                android:layout_marginEnd="8dp"
                                android:gravity="center">

                                <ImageView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:src="@drawable/day5"
                                    android:layout_marginBottom="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="YOU GET"
                                    android:textColor="#FFD700"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:layout_marginBottom="8dp" />

                                <TextView
                                    android:id="@+id/tvInviteCoins"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="₹60"
                                    android:textColor="@color/white"
                                    android:textSize="24sp"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <!-- Friend's Reward -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:background="#1A1A1A"
                                android:padding="20dp"
                                android:layout_marginStart="8dp"
                                android:gravity="center">

                                <ImageView
                                    android:layout_width="60dp"
                                    android:layout_height="60dp"
                                    android:src="@drawable/day5"
                                    android:layout_marginBottom="12dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="FRIEND GETS"
                                    android:textColor="#FFD700"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:layout_marginBottom="8dp" />

                                <TextView
                                    android:id="@+id/txtchipsbelow"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="₹60"
                                    android:textColor="@color/white"
                                    android:textSize="24sp"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Share Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="@drawable/frame"
                        android:padding="25dp"
                        android:layout_marginBottom="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:text="📱 SHARE WITH FRIENDS"
                            android:textColor="@color/white"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="20dp" />

                        <LinearLayout
                            android:id="@+id/lnrsocial"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center">

                            <!-- WhatsApp -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:background="#1A1A1A"
                                android:padding="15dp"
                                android:layout_marginEnd="8dp"
                                android:clickable="true"
                                android:focusable="true">

                                <ImageView
                                    android:id="@+id/imgwhats"
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:src="@drawable/whaatup"
                                    android:layout_marginBottom="8dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="WhatsApp"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <!-- Facebook -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:background="#1A1A1A"
                                android:padding="15dp"
                                android:layout_marginHorizontal="4dp"
                                android:clickable="true"
                                android:focusable="true">

                                <ImageView
                                    android:id="@+id/imgfb"
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:src="@drawable/facebook"
                                    android:layout_marginBottom="8dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Facebook"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />
                            </LinearLayout>

                            <!-- Email -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:background="#1A1A1A"
                                android:padding="15dp"
                                android:layout_marginStart="8dp"
                                android:clickable="true"
                                android:focusable="true">

                                <ImageView
                                    android:id="@+id/imgmail"
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:src="@drawable/mailsocial"
                                    android:layout_marginBottom="8dp" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Email"
                                    android:textColor="@color/white"
                                    android:textSize="12sp"
                                    android:textStyle="bold" />
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>

                    <!-- Terms Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:background="#1A1A1A"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📋 Terms &amp; Conditions"
                            android:textColor="#FFD700"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="15dp" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/txtyourfrind"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🎯 INVITE LIMIT: 10 FRIENDS"
                                android:textColor="@color/white"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:layout_marginBottom="12dp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="✅ Maximum 10 referrals per account\n✅ Referred friend must be a new user\n✅ Rewards credited within 24 hours\n✅ Subject to verification and approval\n✅ Gaming27.com reserves the right to modify terms"
                                android:textColor="@color/colordullwhite"
                                android:textSize="12sp"
                                android:lineSpacingExtra="4dp" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </ScrollView>
        </RelativeLayout>

        <include layout="@layout/dialog_toolbar"/>
    </RelativeLayout>
</RelativeLayout>
