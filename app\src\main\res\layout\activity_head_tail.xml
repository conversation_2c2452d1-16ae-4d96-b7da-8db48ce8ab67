<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg2"
    android:gravity="center"
    android:paddingLeft="0dp"
    android:paddingTop="0dp"
    android:paddingRight="0dp"
    android:paddingBottom="0dp"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context="._DragonTiger.DragonTiger_A">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imgback"
            android:layout_width="@dimen/dp40"
            android:layout_height="@dimen/dp40"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/back"
            android:visibility="visible" />

        <ImageView
            android:layout_width="@dimen/dimen_40dp"
            android:layout_height="@dimen/dimen_40dp"
            android:layout_below="@+id/imgback"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="20dp"
            android:onClick="openGameRules"
            android:src="@drawable/ic_jackpot_info"
            android:visibility="visible"
            tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />


    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <!--// Also change from table image if changing-->
        <RelativeLayout
            android:id="@+id/rtlAmpire"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/imgTable"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="-90dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/poker11"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/ChipstoDealer"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_centerInParent="true"
                android:visibility="visible" />

            <ImageView
                android:id="@+id/imgTip"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="-110dp"
                android:layout_marginTop="30dp"
                android:src="@drawable/tip"
                android:visibility="gone" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rlt_cards"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="-10dp"
            android:elevation="@dimen/dimen_10dp"
            android:orientation="horizontal"
            android:visibility="visible">

            <LinearLayout
                android:id="@+id/lnrGameCardsView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="60dp"
                    android:layout_height="@dimen/dp60"
                    android:layout_marginTop="@dimen/dp20"
                    android:layout_marginRight="@dimen/dp20"
                    android:src="@drawable/heads" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="@dimen/dt_card_width"
                        android:layout_height="@dimen/dt_card_hight"
                        android:src="@drawable/backside_card"
                        android:visibility="visible" />

                    <ImageView
                        android:id="@+id/ivDragonCard"
                        android:layout_width="@dimen/dt_card_width"
                        android:layout_height="@dimen/dt_card_hight"
                        android:visibility="visible" />
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/lnrvs"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/img_verse"
                        android:layout_width="70dp"
                        android:layout_height="@dimen/dp50"
                        android:src="@drawable/ic_verse" />

                    <RelativeLayout
                        android:layout_width="@dimen/dp60"
                        android:layout_height="@dimen/dp60"
                        android:layout_below="@id/img_verse"
                        android:layout_centerHorizontal="true"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_centerHorizontal="true"
                            android:scaleType="centerCrop"
                            android:src="@drawable/watch"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/txtcountdown"
                            style="@style/ShadowWhiteTextview"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="@dimen/dp20"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text=""
                            android:textColor="#EEC283"
                            android:textSize="@dimen/dp15"
                            android:textStyle="bold"
                            android:visibility="invisible" />

                        <TextView
                            android:id="@+id/tvStartTimer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="@dimen/dp20"
                            android:shadowColor="#5d5534"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="0"
                            android:textColor="#5d5534"
                            android:textSize="@dimen/dp15"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/tvStartTimer_"
                            style="@style/ShadowWhiteTextview"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center"
                            android:layout_marginTop="-10dp"
                            android:gravity="center"
                            android:text="00"
                            android:textColor="#5d5534"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:visibility="invisible" />
                    </RelativeLayout>


                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="@dimen/dt_card_width"
                        android:layout_height="@dimen/dt_card_hight"
                        android:src="@drawable/backside_card" />

                    <ImageView
                        android:id="@+id/ivTigerCard"
                        android:layout_width="@dimen/dt_card_width"
                        android:layout_height="@dimen/dt_card_hight" />
                </RelativeLayout>

                <ImageView
                    android:layout_width="@dimen/dp60"
                    android:layout_height="@dimen/dp60"
                    android:layout_marginLeft="@dimen/dp20"
                    android:layout_marginTop="@dimen/dp20"
                    android:src="@drawable/tails" />

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivGadhi"
                        android:layout_width="@dimen/dt_card_width"
                        android:layout_height="@dimen/dt_card_hight"
                        android:layout_centerHorizontal="true"
                        android:src="@drawable/ic_dt_tigerdragon_card"
                        android:visibility="gone" />
                </RelativeLayout>
            </LinearLayout>
        </RelativeLayout>

        <ImageView
            android:id="@+id/imgTable"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/rltGameDetails"
            android:layout_alignTop="@id/rltGameDetails"
            android:layout_alignRight="@id/rltGameDetails"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="-20dp"
            android:layout_marginEnd="-20dp"
            android:layout_marginBottom="@dimen/dp50"
            android:background="@drawable/ic_table_head" />

        <RelativeLayout
            android:id="@+id/rltGameDetails"
            android:layout_width="600dp"
            android:layout_height="wrap_content"
            android:layout_below="@+id/rlt_cards"
            android:layout_centerInParent="true"
            android:layout_marginTop="-30dp"
            android:gravity="center"
            android:paddingLeft="@dimen/dimen_50dp"
            android:paddingTop="@dimen/dimen_40dp"
            android:paddingBottom="@dimen/dimen_50dp">

            <RelativeLayout
                android:id="@+id/rltlastwine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginHorizontal="@dimen/dp100"
                android:layout_marginTop="-5dp"
                android:layout_marginBottom="5dp"
                android:gravity="center_horizontal">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_alignLeft="@id/horizontal"
                    android:layout_alignTop="@id/horizontal"
                    android:layout_alignRight="@id/ivMorewins"
                    android:layout_alignBottom="@id/horizontal"
                    android:background="@drawable/ic_jackpot_change_strip" />

                <HorizontalScrollView
                    android:id="@+id/horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_toLeftOf="@+id/ivMorewins"
                    android:fillViewport="true"
                    android:paddingLeft="5dp"
                    android:paddingTop="3dp"
                    android:paddingRight="5dp"
                    android:paddingBottom="3dp"
                    android:scrollbars="none"
                    tools:ignore="SpeakableTextPresentCheck">

                    <LinearLayout
                        android:id="@+id/lnrcancelist"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <!--                        <include layout="@layout/item_lastbet" />-->
                    </LinearLayout>
                </HorizontalScrollView>

                <ImageView
                    android:id="@+id/ivMorewins"
                    android:layout_width="50dp"
                    android:layout_height="30dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/btn_oragen_drop"
                    android:onClick="openJackpotLasrWinHistory"
                    android:padding="@dimen/dp7"
                    android:src="@drawable/ic_arrow_zigzag"
                    android:visibility="gone"
                    app:tint="@color/blue" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/rltlastwine"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center_horizontal"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="-5dp"
                    android:layout_marginRight="5dp"
                    android:layout_marginBottom="@dimen/dp30"
                    android:orientation="horizontal">

                    <RelativeLayout
                        android:id="@+id/rltDragon"
                        android:layout_width="@dimen/dp130"
                        android:layout_height="@dimen/dp150"
                        android:layout_gravity="center_vertical"
                        android:layout_marginTop="@dimen/dp10"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_marginBottom="@dimen/dp10"
                        android:background="@drawable/head_last"
                        android:padding="15dp">
                        <!--android:background="@drawable/head_bg"-->
                        <TextView
                            android:id="@+id/txtandar"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="Head"
                            android:textAllCaps="true"
                            android:textColor="#EEC283"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/txtandar"
                            android:layout_centerHorizontal="true"
                            android:text="1:1"
                            android:textColor="@color/white"
                            android:textSize="10sp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_marginTop="20dp"
                            android:gravity="center_vertical">

                            <RelativeLayout
                                android:id="@+id/rltDragonChips"
                                android:layout_width="@dimen/dt_putchips_size"
                                android:layout_height="@dimen/dt_putchips_size"
                                android:layout_centerInParent="true"
                                android:layout_marginRight="@dimen/dp10"
                                android:background="@drawable/ic_dt_chips"
                                android:visibility="visible">

                                <TextView
                                    android:id="@+id/tvDragonCoins"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:ellipsize="end"
                                    android:padding="3dp"
                                    android:shadowColor="@color/black"
                                    android:shadowDx="1"
                                    android:shadowDy="1"
                                    android:shadowRadius="3"
                                    android:text="1"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="14dp"
                                    android:textStyle="bold"
                                    android:visibility="gone" />
                            </RelativeLayout>

                            <TextView
                                android:id="@+id/tvDragonAddedAmt"
                                style="@style/ShadowWhiteTextview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="13sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="5dp"
                                android:layout_height="match_parent"
                                android:layout_marginHorizontal="@dimen/dp3"
                                android:text="/"
                                android:visibility="gone" />

                            <TextView
                                android:id="@+id/tvDragonTotalAmt"
                                style="@style/ShadowWhiteTextview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:textSize="13sp"
                                android:textStyle="bold"
                                android:visibility="gone" />
                        </LinearLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rltTie"
                        android:layout_width="120dp"
                        android:layout_height="160dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@drawable/headtail"
                        android:gravity="center"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/tvTie"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="TIE"
                            android:textAllCaps="true"
                            android:textColor="#EEC283"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/tvTie"
                            android:layout_centerHorizontal="true"
                            android:text="11:1"
                            android:textSize="10sp" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content">

                            <RelativeLayout
                                android:id="@+id/rltTieChips"
                                android:layout_width="@dimen/dt_putchips_size"
                                android:layout_height="@dimen/dt_putchips_size"
                                android:layout_centerInParent="true"
                                android:layout_marginRight="@dimen/dp10"
                                android:background="@drawable/ic_dt_chips"
                                android:visibility="visible">

                                <TextView
                                    android:id="@+id/tvTieCoins"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:ellipsize="end"
                                    android:padding="3dp"
                                    android:shadowColor="@color/black"
                                    android:shadowDx="1"
                                    android:shadowDy="1"
                                    android:shadowRadius="3"
                                    android:text="1"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="14dp"
                                    android:textStyle="bold"
                                    android:visibility="gone" />
                            </RelativeLayout>

                            <TextView
                                android:id="@+id/tvTieAddedAmt"
                                style="@style/ShadowWhiteTextview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="5dp"
                                android:layout_height="match_parent"
                                android:layout_marginHorizontal="@dimen/dp3"
                                android:text="/" />

                            <TextView
                                android:id="@+id/tvTieTotalAmt"
                                style="@style/ShadowWhiteTextview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0000"
                                android:textSize="14sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/rltTiger"
                        android:layout_width="@dimen/dp130"
                        android:layout_height="@dimen/dp150"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_marginBottom="@dimen/dp10"
                        android:background="@drawable/tail_last"
                        android:padding="15dp">

                        <TextView
                            android:id="@+id/txtBahar"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerHorizontal="true"
                            android:layout_marginLeft="10dp"
                            android:layout_marginTop="3dp"
                            android:ellipsize="end"
                            android:shadowColor="@color/black"
                            android:shadowDx="1"
                            android:shadowDy="1"
                            android:shadowRadius="3"
                            android:text="Tail"
                            android:textAllCaps="true"
                            android:textColor="#EEC283"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/txtBahar"
                            android:layout_centerHorizontal="true"
                            android:text="1:1"
                            android:textColor="@color/white"
                            android:textSize="10sp"
                            android:visibility="gone" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_marginTop="20dp"
                            android:gravity="center_vertical">

                            <RelativeLayout
                                android:id="@+id/rltTigerChips"
                                android:layout_width="@dimen/dt_putchips_size"
                                android:layout_height="@dimen/dt_putchips_size"
                                android:layout_centerInParent="true"
                                android:layout_marginRight="@dimen/dp10"
                                android:background="@drawable/ic_dt_chips"
                                android:visibility="visible">

                                <TextView
                                    android:id="@+id/tvTigerChips"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerInParent="true"
                                    android:ellipsize="end"
                                    android:padding="3dp"
                                    android:shadowColor="@color/black"
                                    android:shadowDx="1"
                                    android:shadowDy="1"
                                    android:shadowRadius="3"
                                    android:text="1"
                                    android:textColor="@color/colorPrimary"
                                    android:textSize="14dp"
                                    android:textStyle="bold"
                                    android:visibility="gone" />
                            </RelativeLayout>

                            <TextView
                                android:id="@+id/tvTigerAddedAmt"
                                style="@style/ShadowWhiteTextview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="13sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="5dp"
                                android:layout_height="match_parent"
                                android:layout_marginHorizontal="@dimen/dp3"
                                android:visibility="gone"
                                android:text="/" />

                            <TextView
                                android:id="@+id/tvTigerTotalAmt"
                                style="@style/ShadowWhiteTextview"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:visibility="gone"
                                android:textSize="13sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </RelativeLayout>
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>
        <!--  Players Layout START -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone">
            <!--            player2-->
            <RelativeLayout
                android:id="@+id/rltplayer2"
                android:layout_width="@dimen/player_width"
                android:layout_height="@dimen/player_height"
                android:layout_marginBottom="-135dp">

                <RelativeLayout
                    android:id="@+id/rltcirclproimage2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/txtPlay2"
                    android:layout_centerHorizontal="true">

                    <ImageView
                        android:id="@+id/imgpl2glow"
                        android:layout_width="@dimen/Player_glow_width"
                        android:layout_height="@dimen/Player_glow_height"
                        android:layout_centerInParent="true"
                        android:src="@drawable/glow_circle"
                        android:visibility="gone" />

                    <RelativeLayout
                        android:layout_width="@dimen/Player_rt_size"
                        android:layout_height="@dimen/Player_rt_size"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/user_bg_circle">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/imgpl2circle"
                            android:layout_width="@dimen/Player_circle_size"
                            android:layout_height="@dimen/Player_circle_size"
                            android:layout_centerInParent="true"
                            android:src="@drawable/avatar"
                            android:visibility="visible" />

                        <ProgressBar
                            android:id="@+id/circularProgressbar2"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="@dimen/Player_progress_size"
                            android:layout_height="@dimen/Player_progress_size"
                            android:layout_centerInParent="true"
                            android:indeterminate="false"
                            android:max="100"
                            android:progress="50"
                            android:progressDrawable="@drawable/circular"
                            android:secondaryProgress="100"
                            android:visibility="visible" />
                    </RelativeLayout>

                    <TextView
                        android:id="@+id/txtwinner2"
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/black_transparent"
                        android:gravity="center"
                        android:text="Winner"
                        android:textColor="#ffffff"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/txtPlay2"
                    style="@style/UserNameTextStyle"
                    android:text="Player 2" />

                <LinearLayout
                    android:id="@+id/lnruserdetails2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rltcirclproimage2"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/wallet_text_margin_top"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/white_lable_small"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txtPlay2wallet"
                            style="@style/BlackTextview" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rltwinnersymble2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/rltplayer2"
                android:layout_marginBottom="-35dp"
                android:visibility="visible">

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/giphy"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/star"
                    android:visibility="visible" />
            </RelativeLayout>
            <!--            player2 end-->
            <!--            player2 NEW-->
            <RelativeLayout
                android:id="@+id/lnr_new2player"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginRight="-170dp"
                android:layout_marginBottom="-110dp"
                android:visibility="gone">

                <RelativeLayout
                    android:id="@+id/rltplayer2_new"
                    android:layout_width="235dp"
                    android:layout_height="100dp">

                    <RelativeLayout
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_alignParentTop="true"
                        android:layout_centerHorizontal="true"
                        android:alpha="0.1"
                        android:background="#FFC107"
                        android:visibility="gone" />

                    <RelativeLayout
                        android:id="@+id/rltcirclproimage2_new"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true">

                        <ImageView
                            android:id="@+id/imgpl2_newglow"
                            android:layout_width="75dp"
                            android:layout_height="75dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/glow_circle"
                            android:visibility="gone" />

                        <RelativeLayout
                            android:layout_width="67dp"
                            android:layout_height="67dp"
                            android:background="@drawable/user_bg_circle">

                            <de.hdodenhof.circleimageview.CircleImageView
                                android:id="@+id/imgpl2_newcircle"
                                android:layout_width="57dp"
                                android:layout_height="57dp"
                                android:layout_centerInParent="true"
                                android:src="@drawable/avatar"
                                android:visibility="visible" />

                            <ProgressBar
                                android:id="@+id/circularProgressbar2_new"
                                style="?android:attr/progressBarStyleHorizontal"
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:layout_centerInParent="true"
                                android:indeterminate="false"
                                android:max="100"
                                android:progress="50"
                                android:progressDrawable="@drawable/circular"
                                android:secondaryProgress="100"
                                android:visibility="visible" />
                        </RelativeLayout>

                        <TextView
                            android:id="@+id/txtwinner2_new"
                            android:layout_width="55dp"
                            android:layout_height="55dp"
                            android:layout_centerInParent="true"
                            android:background="@drawable/black_transparent"
                            android:gravity="center"
                            android:text="Winner"
                            android:textColor="#ffffff"
                            android:textSize="12sp"
                            android:visibility="gone" />
                    </RelativeLayout>

                    <TextView
                        android:id="@+id/txtPlay2_new"
                        android:layout_width="70dp"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="-20dp"
                        android:layout_toRightOf="@+id/rltcirclproimage2_new"
                        android:ellipsize="end"
                        android:shadowColor="@color/black"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="3"
                        android:singleLine="true"
                        android:text=""
                        android:textColor="@color/colordullwhite"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/rltcirclproimage2_new"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:background="@drawable/white_lable_small"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:id="@+id/txtPlay2_newwallet"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:textColor="@color/black"
                                android:textSize="10dp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </LinearLayout>
                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/rltwinnersymble2_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/rltplayer2_new"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="-35dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="150dp"
                        android:layout_height="100dp"
                        android:layout_centerHorizontal="true"
                        android:src="@drawable/giphy"
                        android:visibility="visible" />

                    <ImageView
                        android:layout_width="150dp"
                        android:layout_height="100dp"
                        android:layout_centerHorizontal="true"
                        android:src="@drawable/star"
                        android:visibility="visible" />
                </RelativeLayout>
            </RelativeLayout>
            <!--            player2 New end -->
            <!--            player3-->
            <RelativeLayout
                android:id="@+id/rltplayer3"
                android:layout_width="@dimen/player_width"
                android:layout_height="@dimen/player_height"
                android:layout_marginBottom="-100dp"
                android:layout_toRightOf="@+id/rltplayer2"
                android:visibility="visible">

                <RelativeLayout
                    android:layout_width="@dimen/Player_glow_width"
                    android:layout_height="@dimen/Player_glow_height"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:alpha="0.1"
                    android:background="#FFC107"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/rltcirclproimage3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/txtPlay3"
                    android:layout_centerHorizontal="true">

                    <ImageView
                        android:id="@+id/imgpl3glow"
                        android:layout_width="@dimen/Player_glow_width"
                        android:layout_height="@dimen/Player_glow_height"
                        android:layout_centerInParent="true"
                        android:src="@drawable/glow_circle"
                        android:visibility="gone" />

                    <RelativeLayout
                        android:layout_width="@dimen/Player_rt_size"
                        android:layout_height="@dimen/Player_rt_size"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/user_bg_circle">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/imgpl3circle"
                            android:layout_width="@dimen/Player_circle_size"
                            android:layout_height="@dimen/Player_circle_size"
                            android:layout_centerInParent="true"
                            android:src="@drawable/avatar"
                            android:visibility="visible" />

                        <ProgressBar
                            android:id="@+id/circularProgressbar3"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="@dimen/Player_progress_size"
                            android:layout_height="@dimen/Player_progress_size"
                            android:layout_centerInParent="true"
                            android:indeterminate="false"
                            android:max="100"
                            android:progress="50"
                            android:progressDrawable="@drawable/circular"
                            android:secondaryProgress="100"
                            android:visibility="visible" />
                    </RelativeLayout>

                    <TextView
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/black_transparent"
                        android:gravity="center"
                        android:text="Winner"
                        android:textColor="#ffffff"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/txtPlay3"
                    style="@style/UserNameTextStyle"
                    android:text="Player 3" />

                <LinearLayout
                    android:id="@+id/lnruserdetails3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rltcirclproimage3"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/wallet_text_margin_top"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/white_lable_small"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txtPlay3wallet"
                            style="@style/BlackTextview" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rltwinnersymble3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/rltplayer3"
                android:layout_marginBottom="-30dp"
                android:layout_toRightOf="@+id/rltplayer2"
                android:visibility="visible">

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/giphy"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/star"
                    android:visibility="visible" />
            </RelativeLayout>
            <!--            player end-->
            <!--            player4-->
            <!--        android:layout_toRightOf="@+id/rltplayer3"-->
            <RelativeLayout
                android:id="@+id/rltplayer4"
                android:layout_width="@dimen/player_width"
                android:layout_height="@dimen/player_height"
                android:layout_marginBottom="-100dp"
                android:layout_toLeftOf="@+id/rltplayer5"
                android:visibility="visible">

                <RelativeLayout
                    android:layout_width="@dimen/Player_glow_width"
                    android:layout_height="@dimen/Player_glow_height"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:alpha="0.1"
                    android:background="#FFC107"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/rltcirclproimage4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/txtPlay4"
                    android:layout_centerHorizontal="true">

                    <ImageView
                        android:id="@+id/imgpl4glow"
                        android:layout_width="@dimen/Player_glow_width"
                        android:layout_height="@dimen/Player_glow_height"
                        android:layout_centerInParent="true"
                        android:src="@drawable/glow_circle"
                        android:visibility="gone" />

                    <RelativeLayout
                        android:layout_width="@dimen/Player_rt_size"
                        android:layout_height="@dimen/Player_rt_size"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/user_bg_circle">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/imgpl4circle"
                            android:layout_width="@dimen/Player_circle_size"
                            android:layout_height="@dimen/Player_circle_size"
                            android:layout_centerInParent="true"
                            android:src="@drawable/avatar"
                            android:visibility="visible" />

                        <ProgressBar
                            android:id="@+id/circularProgressbar4"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="@dimen/Player_progress_size"
                            android:layout_height="@dimen/Player_progress_size"
                            android:layout_centerInParent="true"
                            android:indeterminate="false"
                            android:max="100"
                            android:progress="50"
                            android:progressDrawable="@drawable/circular"
                            android:secondaryProgress="100"
                            android:visibility="visible" />
                    </RelativeLayout>

                    <TextView
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/black_transparent"
                        android:gravity="center"
                        android:text="Winner"
                        android:textColor="#ffffff"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/txtPlay4"
                    style="@style/UserNameTextStyle"
                    android:text="Player 4" />

                <LinearLayout
                    android:id="@+id/lnruserdetails4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rltcirclproimage4"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/wallet_text_margin_top"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/white_lable_small"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txtPlay4wallet"
                            style="@style/BlackTextview" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rltwinnersymble4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/rltplayer4"
                android:layout_marginBottom="-30dp"
                android:layout_toLeftOf="@+id/rltplayer5"
                android:visibility="visible">

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/giphy"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/star"
                    android:visibility="visible" />
            </RelativeLayout>
            <!--            player4 end-->
            <!--            player5-->
            <RelativeLayout
                android:id="@+id/rltplayer5"
                android:layout_width="@dimen/player_width"
                android:layout_height="@dimen/player_height"
                android:layout_centerVertical="true"
                android:layout_marginBottom="-135dp"
                android:visibility="visible">

                <RelativeLayout
                    android:id="@+id/rltcirclproimage5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/txtPlay5"
                    android:layout_centerHorizontal="true">

                    <ImageView
                        android:id="@+id/imgpl5glow"
                        android:layout_width="@dimen/Player_glow_width"
                        android:layout_height="@dimen/Player_glow_height"
                        android:layout_centerInParent="true"
                        android:src="@drawable/glow_circle"
                        android:visibility="gone" />

                    <RelativeLayout
                        android:layout_width="@dimen/Player_rt_size"
                        android:layout_height="@dimen/Player_rt_size"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/user_bg_circle">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/imgpl5circle"
                            android:layout_width="@dimen/Player_circle_size"
                            android:layout_height="@dimen/Player_circle_size"
                            android:layout_centerInParent="true"
                            android:src="@drawable/avatar"
                            android:visibility="visible" />

                        <ProgressBar
                            android:id="@+id/circularProgressbar5"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="@dimen/Player_progress_size"
                            android:layout_height="@dimen/Player_progress_size"
                            android:layout_centerInParent="true"
                            android:indeterminate="false"
                            android:max="100"
                            android:progress="50"
                            android:progressDrawable="@drawable/circular"
                            android:secondaryProgress="100"
                            android:visibility="visible" />
                    </RelativeLayout>

                    <TextView
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/black_transparent"
                        android:gravity="center"
                        android:text="Winner"
                        android:textColor="#ffffff"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/txtPlay5"
                    style="@style/UserNameTextStyle"
                    android:text="Player 5" />

                <LinearLayout
                    android:id="@+id/lnruserdetails5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rltcirclproimage5"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/wallet_text_margin_top"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/white_lable_small"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txtPlay5wallet"
                            style="@style/BlackTextview" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rltwinnersymble5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/rltplayer5"
                android:layout_marginBottom="-30dp"
                android:visibility="visible">

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/giphy"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/star"
                    android:visibility="visible" />
            </RelativeLayout>
            <!--            player5 end-->
            <!--            player6 start-->
            <RelativeLayout
                android:id="@+id/rltplayer6"
                android:layout_width="@dimen/player_width"
                android:layout_height="@dimen/player_height"
                android:layout_marginBottom="-100dp"
                android:layout_toRightOf="@+id/rltplayer5"
                android:visibility="gone">

                <RelativeLayout
                    android:id="@+id/rltcirclproimage6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/txtPlay6"
                    android:layout_centerHorizontal="true">

                    <ImageView
                        android:id="@+id/imgpl6glow"
                        android:layout_width="@dimen/Player_glow_width"
                        android:layout_height="@dimen/Player_glow_width"
                        android:layout_centerInParent="true"
                        android:src="@drawable/glow_circle"
                        android:visibility="gone" />

                    <RelativeLayout
                        android:layout_width="@dimen/Player_rt_size"
                        android:layout_height="@dimen/Player_rt_size"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/user_bg_circle">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/imgpl6circle"
                            android:layout_width="@dimen/Player_circle_size"
                            android:layout_height="@dimen/Player_circle_size"
                            android:layout_centerInParent="true"
                            android:src="@drawable/avatar"
                            android:visibility="visible" />

                        <ProgressBar
                            android:id="@+id/circularProgressbar6"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="@dimen/Player_progress_size"
                            android:layout_height="@dimen/Player_progress_size"
                            android:layout_centerInParent="true"
                            android:indeterminate="false"
                            android:max="100"
                            android:progress="50"
                            android:progressDrawable="@drawable/circular"
                            android:secondaryProgress="100"
                            android:visibility="visible" />
                    </RelativeLayout>

                    <TextView
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/black_transparent"
                        android:gravity="center"
                        android:text="Winner"
                        android:textColor="#ffffff"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/txtPlay6"
                    style="@style/UserNameTextStyle"
                    android:text="Player 6" />

                <LinearLayout
                    android:id="@+id/lnruserdetails6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rltcirclproimage6"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/wallet_text_margin_top"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/white_lable_small"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txtPlay6wallet"
                            style="@style/BlackTextview" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rltwinnersymble6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/rltplayer6"
                android:layout_marginBottom="-30dp"
                android:layout_toRightOf="@+id/rltplayer5"
                android:visibility="gone">

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/giphy"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="150dp"
                    android:layout_height="100dp"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/star"
                    android:visibility="visible" />
            </RelativeLayout>
            <!-- player 6 end-->
            <!--  player7 start -->
            <RelativeLayout
                android:id="@+id/rltplayer7"
                android:layout_width="@dimen/player_width"
                android:layout_height="@dimen/player_height"
                android:layout_marginBottom="-135dp"
                android:layout_toRightOf="@+id/rltplayer6"
                android:visibility="gone">

                <RelativeLayout
                    android:id="@+id/rltcirclproimage7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/txtPlay7"
                    android:layout_centerHorizontal="true">

                    <ImageView
                        android:id="@+id/imgpl7glow"
                        android:layout_width="@dimen/Player_glow_width"
                        android:layout_height="@dimen/Player_glow_width"
                        android:layout_centerInParent="true"
                        android:src="@drawable/glow_circle"
                        android:visibility="gone" />

                    <RelativeLayout
                        android:layout_width="@dimen/Player_rt_size"
                        android:layout_height="@dimen/Player_rt_size"
                        android:layout_centerHorizontal="true"
                        android:background="@drawable/user_bg_circle">

                        <de.hdodenhof.circleimageview.CircleImageView
                            android:id="@+id/imgpl7circle"
                            android:layout_width="@dimen/Player_circle_size"
                            android:layout_height="@dimen/Player_circle_size"
                            android:layout_centerInParent="true"
                            android:src="@drawable/avatar"
                            android:visibility="visible" />

                        <ProgressBar
                            android:id="@+id/circularProgressbar7"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="@dimen/Player_progress_size"
                            android:layout_height="@dimen/Player_progress_size"
                            android:layout_centerInParent="true"
                            android:indeterminate="false"
                            android:max="100"
                            android:progress="50"
                            android:progressDrawable="@drawable/circular"
                            android:secondaryProgress="100"
                            android:visibility="visible" />
                    </RelativeLayout>

                    <TextView
                        android:layout_width="55dp"
                        android:layout_height="55dp"
                        android:layout_centerInParent="true"
                        android:background="@drawable/black_transparent"
                        android:gravity="center"
                        android:text="Winner"
                        android:textColor="#ffffff"
                        android:textSize="12sp"
                        android:visibility="gone" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/txtPlay7"
                    style="@style/UserNameTextStyle"
                    android:text="Player 7" />

                <LinearLayout
                    android:id="@+id/lnruserdetails7"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/rltcirclproimage7"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/wallet_text_margin_top"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/white_lable_small"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/txtPlay7wallet"
                            style="@style/BlackTextview" />
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rltwinnersymble7"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/rltplayer7"
                android:layout_toRightOf="@+id/rltplayer6"
                android:visibility="gone">

                <ImageView
                    android:layout_width="@dimen/player_width"
                    android:layout_height="@dimen/player_height"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/giphy"
                    android:visibility="visible" />

                <ImageView
                    android:layout_width="@dimen/player_width"
                    android:layout_height="@dimen/player_height"
                    android:layout_centerHorizontal="true"
                    android:src="@drawable/star"
                    android:visibility="visible" />
            </RelativeLayout>
            <!-- player 7 end-->
        </RelativeLayout>
        <!--  Players Layout END -->
        <include
            android:id="@+id/lnrtypegame"
            layout="@layout/view_user_bottom_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true" />

    </RelativeLayout>


    <RelativeLayout
        android:id="@+id/sticker_animation_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />


    <RelativeLayout
        android:id="@+id/rltwinnersymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivWine"
                android:layout_width="wrap_content"
                android:layout_height="150dp"
                android:layout_centerHorizontal="true"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvWine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text="Win"
                android:textColor="@color/white"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivWine"
                app:layout_constraintEnd_toEndOf="@+id/ivWine"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivWine" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </RelativeLayout>


    <RelativeLayout
        android:id="@id/rtllosesymble1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-30dp"
        android:background="#90000000"
        android:gravity="center"
        android:visibility="gone">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/ivlose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvlose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="210dp"
                android:gravity="center"
                android:text="Win"
                android:textColor="@color/BrownColor"
                android:textSize="20sp"
                app:fontFilePath="@string/Helvetica_Bold_Extra"
                app:layout_constraintBottom_toBottomOf="@+id/ivlose"
                app:layout_constraintEnd_toEndOf="@+id/ivlose"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivlose" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rltBetStatus"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#90000000"
        android:visibility="gone">

        <ImageView
            android:id="@+id/ivBetStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true" />

    </RelativeLayout>

    <!--    <RelativeLayout-->
    <!--        android:id="@+id/rltFlipCoins"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:background="#90000000"-->
    <!--        android:visibility="gone"-->
    <!--        >-->

    <!--        <ImageView-->
    <!--            android:id="@+id/coin"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_centerInParent="true"-->
    <!--            android:src="@drawable/heads" />-->

    <!--    </RelativeLayout>-->
    <RelativeLayout
        android:id="@+id/rltFlipCoins"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#90000000"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="400dp"
            android:layout_height="250dp"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:visibility="visible">

            <ImageView
                android:id="@+id/coin"
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@drawable/heads" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="30dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/txtGameRunning"
            android:layout_width="@dimen/dp300"
            android:layout_height="wrap_content"
            android:src="@drawable/waiting_for_next"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/txtGameBets"
            style="@style/ShadowWhiteTextview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="@dimen/dp40"
            android:layout_marginTop="@dimen/dp60"
            android:gravity="center"
            android:src="@drawable/place_your_bet"
            android:text="Place your bet"
            android:textColor="#EEC283"
            android:textSize="20dp"
            android:textStyle="bold"
            android:visibility="gone" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginLeft="@dimen/dp80"
        android:orientation="vertical"
        android:visibility="invisible"
        android:weightSum="4">

        <TextView
            android:id="@+id/left_user1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:text="User 1" />

        <TextView
            android:id="@+id/left_user2"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="bottom|center"
            android:text="User 2" />

        <TextView
            android:id="@+id/left_user3"
            android:layout_width="wrap_content"
            android:layout_height="250dp"
            android:layout_weight="1"
            android:gravity="bottom|center"
            android:text="User 3" />

        <TextView
            android:id="@+id/left_user4"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="bottom|center"
            android:text="User 4"
            android:visibility="invisible" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/dp60"
        android:orientation="vertical"
        android:visibility="invisible"
        android:weightSum="4">

        <TextView
            android:id="@+id/right_user1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="start|center"
            android:layout_weight="1"
            android:text="User 1" />

        <TextView
            android:id="@+id/right_user2"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="start|center"
            android:layout_weight="1"
            android:text="User 2" />

        <TextView
            android:id="@+id/right_user3"
            android:layout_width="wrap_content"
            android:layout_height="250dp"
            android:layout_gravity="start|center"
            android:layout_weight="1"
            android:text="User 3" />

        <TextView
            android:id="@+id/right_user4"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="start|center"
            android:layout_weight="1"
            android:text="User 4"
            android:visibility="invisible" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycle_bots"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="false"
        android:layout_centerInParent="true"
        android:layout_marginLeft="@dimen/dp50"
        android:visibility="visible"
        tools:itemCount="3"
        tools:listitem="@layout/item_bots" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycle_bots_right"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_centerInParent="true"
        android:layout_centerHorizontal="true"
        android:layout_marginRight="@dimen/dp20"
        android:visibility="visible"
        tools:itemCount="3"
        tools:listitem="@layout/item_bots" />


</RelativeLayout>