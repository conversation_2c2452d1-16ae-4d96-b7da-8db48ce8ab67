<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout
        android:layout_width="400dp"
        android:layout_height="@dimen/payu_dimen_200dp"
        android:layout_centerInParent="true"
        android:background="@drawable/dialog_bg_blank_new">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

    <EditText
        android:id="@+id/edtTableId"
        android:layout_width="250dp"
        android:layout_height="@dimen/dp35"
        android:hint="Enter Table Id"
        android:inputType="number"
        android:layout_gravity="center_horizontal"
        style="@style/EditTextWithBackground_new"
        android:textAppearance="?android:attr/textAppearanceLarge" />



    <ImageView
        android:id="@+id/imgJoinTable"
        android:layout_marginTop="25dp"
        android:layout_gravity="center_horizontal"
        android:layout_width="250dp"
        android:layout_height="45dp"
        android:src="@drawable/jointablebutton"
        android:text="Join Table" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>