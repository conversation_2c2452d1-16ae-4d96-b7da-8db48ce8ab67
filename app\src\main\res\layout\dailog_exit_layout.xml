<?xml version="1.1" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">


    <RelativeLayout
        android:layout_width="400dp"
        android:layout_height="@dimen/payu_dimen_200dp"
        android:layout_centerInParent="true"
        android:background="@drawable/dialog_exit_bg_new">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp130"
            android:layout_marginBottom="10dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/btn_yes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginRight="10dp"
                android:backgroundTint="@color/transparent"
                app:cardCornerRadius="5dp">

                <RelativeLayout
                    android:layout_width="@dimen/dp100"
                    android:layout_height="@dimen/payu_dimen_45dp"
                    android:background="@drawable/exit_yes_btn">

                </RelativeLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/bt_no"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:backgroundTint="@color/transparent"
                app:cardCornerRadius="5dp">

                <RelativeLayout
                    android:layout_width="@dimen/dp100"
                    android:layout_height="@dimen/payu_dimen_45dp"
                    android:background="@drawable/exit_no_btn"
                    android:paddingLeft="20dp"
                    android:paddingTop="8dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="8dp"></RelativeLayout>

            </androidx.cardview.widget.CardView>


        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>